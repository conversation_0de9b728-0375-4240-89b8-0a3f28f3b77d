import { create } from "zustand";
import type { AlertSend } from "../types";

// 定义选中行状态的接口
interface SelectedRowsState {
  // 选中的行数据
  selectedRows: AlertSend[];

  // 设置选中的行
  setSelectedRows: (rows: AlertSend[]) => void;
  // 移除选中行
  removeSelectedRow: (rowId: number) => void;

  // 获取选择行
  getSelectedRows: () => AlertSend[];
}

export const useSelectedRowsInDrawerStore = create<SelectedRowsState>(
  (set, get) => ({
    selectedRows: [],

    // 设置选中的行数据
    setSelectedRows: (rows: AlertSend[]) =>
      set({
        selectedRows: rows,
      }),

    // 移除选中行
    removeSelectedRow: (rowId: number) =>
      set((state) => ({
        selectedRows: state.selectedRows.filter((r) => r.id !== rowId),
      })),

    // 获取选择行
    getSelectedRows: () => get().selectedRows,
  })
);
