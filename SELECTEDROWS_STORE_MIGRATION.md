# SelectedRows Store 迁移说明

## 概述
将 `selectedRows` 组件之间的数据同步逻辑从 props 传递改为使用 Zustand store 实现，提高了数据管理的一致性和可维护性。

## 修改的文件

### 1. `src/components/SqlMonitor/components/AlertSendTable.tsx`

#### 主要修改：
- **导入 store**: 添加了 `useSelectedRowsInDrawerStore` 的导入
- **移除 props**: 从 `AlertSendTableProps` 接口中移除了 `selectedKeys` 和 `onSelectionConfirm` 属性
- **使用 store**: 用 store 中的状态替换了本地 props 传递
- **同步逻辑**: 在 `onSelectionChange` 回调中将选择的数据同步到 store
- **清空逻辑**: 在清空选择时同步清空 store 中的数据

#### 具体变更：
```typescript
// 之前的 props 接口
interface AlertSendTableProps {
  contentHeight?: number;
  isSelectionMode?: boolean;
  selectedKeys?: React.Key[];
  onSelectionConfirm?: (selectedAlertSends: AlertSend[]) => void;
}

// 修改后的 props 接口
interface AlertSendTableProps {
  contentHeight?: number;
  isSelectionMode?: boolean;
}

// 使用 store 管理状态
const { 
  selectedRows: storeSelectedRows, 
  setSelectedRows: setStoreSelectedRows 
} = useSelectedRowsInDrawerStore();
```

### 2. `src/components/SqlMonitor/components/tabs/NotificationConfigTab.tsx`

#### 主要修改：
- **导入 store**: 添加了 `useSelectedRowsInDrawerStore` 的导入
- **移除本地状态**: 移除了本地的 `selectedRows` 状态，改为使用 store
- **简化 props**: 移除了传递给 `AlertSendTable` 的 `selectedKeys` 和 `onSelectionConfirm` props
- **同步逻辑**: 在抽屉打开时将当前的 `alertSends` 同步到 store

#### 具体变更：
```typescript
// 使用 store 替换本地状态
const { selectedRows, setSelectedRows } = useSelectedRowsInDrawerStore();

// 移除了本地状态声明
// const [selectedRows, setSelectedRows] = useState<AlertSend[]>([]);

// 简化了 AlertSendTable 的使用
<AlertSendTable 
  contentHeight={800} 
  isSelectionMode={true} 
/>
```

## 数据流变化

### 之前的数据流：
```
NotificationConfigTab (本地状态) 
  ↓ props (selectedKeys, onSelectionConfirm)
AlertSendTable 
  ↓ useSelection hook
表格选择状态
  ↓ onSelectionConfirm 回调
NotificationConfigTab (更新本地状态)
```

### 修改后的数据流：
```
NotificationConfigTab 
  ↓ 直接使用 store
useSelectedRowsInDrawerStore (全局状态)
  ↑ setSelectedRows
AlertSendTable 
  ↓ useSelection hook
表格选择状态
  ↓ onSelectionChange 回调
useSelectedRowsInDrawerStore (同步更新)
```

## 优势

1. **状态一致性**: 使用全局 store 确保所有组件看到的选择状态都是一致的
2. **减少 props 传递**: 消除了深层次的 props 传递，简化了组件接口
3. **更好的可维护性**: 选择逻辑集中在 store 中，便于维护和调试
4. **解耦组件**: 组件之间不再直接依赖 props 传递，降低了耦合度

## 测试建议

1. 测试抽屉打开时选择状态的同步
2. 测试表格选择变化时 store 的更新
3. 测试确认选择后的数据传递
4. 测试清空选择功能
5. 测试跨页面选择的保持

## 注意事项

- store 中的数据在组件卸载时不会自动清空，如需要可以在适当的时机手动清空
- 确保在使用 store 的组件中正确处理数据同步的时机
- 如果有多个地方使用相同的选择逻辑，可以考虑进一步抽象
